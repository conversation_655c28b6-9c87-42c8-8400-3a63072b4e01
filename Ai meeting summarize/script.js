// AI Meeting Transcription - Frontend JavaScript
// Handles file upload, processing, and results display

class AudioTranscriptionApp {
    constructor() {
        this.selectedFile = null;
        this.processingSteps = ['step1', 'step2', 'step3', 'step4'];
        this.currentStep = 0;
        this.results = null;
        
        this.initializeEventListeners();
    }
    
    initializeEventListeners() {
        // File input change
        const fileInput = document.getElementById('audioFile');
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        
        // Drag and drop
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleFileDrop(e));
        uploadArea.addEventListener('click', () => fileInput.click());
    }
    
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.validateAndSetFile(file);
        }
    }
    
    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea').classList.add('dragover');
    }
    
    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea').classList.remove('dragover');
    }
    
    handleFileDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        document.getElementById('uploadArea').classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.validateAndSetFile(files[0]);
        }
    }
    
    validateAndSetFile(file) {
        // Validate file type
        const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/flac', 'audio/ogg'];
        const allowedExtensions = ['.mp3', '.wav', '.m4a', '.flac', '.ogg'];
        
        const fileName = file.name.toLowerCase();
        const isValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));
        
        if (!isValidExtension) {
            this.showError('Please select a valid audio file (MP3, WAV, M4A, FLAC, OGG)');
            return;
        }
        
        // Validate file size (max 100MB)
        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
            this.showError('File size must be less than 100MB');
            return;
        }
        
        this.selectedFile = file;
        this.displayFileInfo(file);
        this.enableProcessButton();
    }
    
    displayFileInfo(file) {
        const fileInfo = document.getElementById('fileInfo');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        
        fileName.textContent = file.name;
        fileSize.textContent = this.formatFileSize(file.size);
        
        fileInfo.style.display = 'block';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    enableProcessButton() {
        const processBtn = document.getElementById('processBtn');
        processBtn.disabled = false;
        processBtn.style.opacity = '1';
    }
    
    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${message}</span>
        `;
        
        // Style the error notification
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f87171;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 500;
            animation: slideInRight 0.3s ease;
        `;
        
        // Add to page
        document.body.appendChild(errorDiv);
        
        // Remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (errorDiv.parentNode) {
                        errorDiv.parentNode.removeChild(errorDiv);
                    }
                }, 300);
            }
        }, 5000);
    }
    
    async simulateProcessing() {
        const steps = [
            { id: 'step1', duration: 1000, name: 'Uploading File' },
            { id: 'step2', duration: 3000, name: 'Speech Recognition' },
            { id: 'step3', duration: 2000, name: 'AI Analysis' },
            { id: 'step4', duration: 1500, name: 'Action Items' }
        ];
        
        let progress = 0;
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');
        
        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            const stepElement = document.getElementById(step.id);
            
            // Mark step as active
            stepElement.classList.add('active');
            
            // Simulate processing time
            await new Promise(resolve => setTimeout(resolve, step.duration));
            
            // Mark step as completed
            stepElement.classList.remove('active');
            stepElement.classList.add('completed');
            
            // Update progress
            progress = ((i + 1) / steps.length) * 100;
            progressFill.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '% Complete';
        }
        
        // Generate mock results
        this.results = this.generateMockResults();
    }
    
    generateMockResults() {
        const fileName = this.selectedFile.name.toLowerCase();
        
        // Generate different results based on filename
        if (fileName.includes('meeting') || fileName.includes('conference')) {
            return {
                metadata: {
                    duration: '15:42',
                    type: 'Team Meeting',
                    sentiment: 'Positive',
                    urgency: 'Medium',
                    confidence: '94%'
                },
                summary: [
                    '📈 Sales Performance: Q3 revenue increased by 18% with strong electronics growth',
                    '👥 Customer Satisfaction: Overall score improved to 8.4/10 with better support response',
                    '🚀 Product Development: Premium electronics line launching Q4 with subscription service',
                    '✅ Action Items: Marketing budget finalization and CRM training scheduled',
                    '💰 Financial Planning: Cost reduction achieved with improved acquisition metrics'
                ],
                topics: ['Sales', 'Customer Service', 'Product Development', 'Marketing', 'Finance'],
                actionItems: [
                    'Marketing team to finalize Q4 campaign budget by Friday',
                    'Product team to complete beta testing by month-end',
                    'Sales team training on new CRM system next week',
                    'Customer feedback analysis report due Tuesday',
                    'Schedule follow-up meeting for next Monday at 10 AM'
                ],
                transcript: `Good morning team, let's begin today's quarterly review meeting.

First agenda item: Sales Performance Analysis
- Q3 revenue increased by 18% compared to Q2
- Electronics category showing strongest growth at 25%
- Customer acquisition cost reduced by 12%
- Average order value up 8% to $185

Second agenda item: Customer Satisfaction Metrics
- Overall satisfaction score: 8.4/10 (up from 8.1)
- Support response time improved to 2.3 hours
- Product return rate decreased to 3.2%
- Net Promoter Score: 72 (industry benchmark: 65)

Third agenda item: Upcoming Product Launches
- Premium electronics line launching Q4
- Subscription service beta testing starts next month
- Mobile app update with AI recommendations
- Partnership with three new suppliers confirmed

Action Items:
1. Marketing team to finalize Q4 campaign budget by Friday
2. Product team to complete beta testing by month-end
3. Sales team training on new CRM system next week
4. Customer feedback analysis report due Tuesday

Next meeting scheduled for next Monday at 10 AM.
Meeting adjourned at 11:30 AM.`
            };
        } else if (fileName.includes('call') || fileName.includes('client')) {
            return {
                metadata: {
                    duration: '8:15',
                    type: 'Client Call',
                    sentiment: 'Positive',
                    urgency: 'Low',
                    confidence: '96%'
                },
                summary: [
                    '🤝 Client Relationship: Discussed ongoing partnership and future opportunities',
                    '💼 Business Development: Explored upselling and cross-selling possibilities',
                    '📋 Service Review: Reviewed current service satisfaction and feedback',
                    '🎯 Next Steps: Follow-up actions and proposal preparation outlined'
                ],
                topics: ['Client Relations', 'Business Development', 'Service Review'],
                actionItems: [
                    'Send detailed proposal for premium services',
                    'Schedule product demonstration next week',
                    'Prepare loyalty program information',
                    'Follow up with email containing discussed details'
                ],
                transcript: `Hello, thank you for taking the time to speak with us today.

I wanted to discuss your recent purchase and see how we can better serve you going forward.

Based on your purchase history, I see you've been a loyal customer for over 3 years. Your spending pattern shows a preference for premium electronics and accessories.

I'd like to propose a few additional products that might interest you:
- Extended warranty for your recent purchase
- Premium accessories bundle with 20% discount
- Monthly subscription service for exclusive deals
- Priority customer support upgrade

Your feedback on our service has been consistently positive, with ratings of 8-9 out of 10.

Would you be interested in learning more about our loyalty program benefits?

Thank you for your time. I'll follow up with an email containing the details we discussed.`
            };
        } else {
            return {
                metadata: {
                    duration: '12:30',
                    type: 'General Meeting',
                    sentiment: 'Neutral',
                    urgency: 'Medium',
                    confidence: '92%'
                },
                summary: [
                    '📝 General Discussion: Key business topics and objectives covered',
                    '🎯 Strategic Planning: Goals and targets for upcoming quarter discussed',
                    '📋 Process Review: Current workflows and efficiency improvements',
                    '🔄 Next Steps: Follow-up actions and timeline established'
                ],
                topics: ['Strategy', 'Operations', 'Planning'],
                actionItems: [
                    'Review current processes and identify improvements',
                    'Prepare quarterly planning documents',
                    'Schedule team alignment sessions',
                    'Update project timelines and deliverables'
                ],
                transcript: `This is a sample transcription of your audio file. In a real implementation, this would contain the actual spoken content from your audio file.

The transcription service would process the audio using advanced speech-to-text technology, providing accurate text conversion with proper punctuation and formatting.

Key features include:
- High accuracy speech recognition
- Multiple speaker identification
- Timestamp markers for important sections
- Automatic punctuation and formatting
- Support for various audio formats and quality levels

The system can handle different accents, background noise, and technical terminology specific to your business domain.`
            };
        }
    }
    
    displayResults() {
        // Show results section
        document.getElementById('resultsSection').style.display = 'block';
        
        // Populate metadata
        document.getElementById('meetingDuration').textContent = this.results.metadata.duration;
        document.getElementById('meetingType').textContent = this.results.metadata.type;
        document.getElementById('meetingSentiment').textContent = this.results.metadata.sentiment;
        document.getElementById('meetingSentiment').className = `value sentiment ${this.results.metadata.sentiment.toLowerCase()}`;
        document.getElementById('meetingUrgency').textContent = this.results.metadata.urgency;
        document.getElementById('meetingUrgency').className = `value urgency ${this.results.metadata.urgency.toLowerCase()}`;
        document.getElementById('transcriptionConfidence').textContent = this.results.metadata.confidence;
        
        // Populate summary points
        const summaryContainer = document.getElementById('summaryPoints');
        summaryContainer.innerHTML = '';
        this.results.summary.forEach(point => {
            const pointDiv = document.createElement('div');
            pointDiv.className = 'summary-point';
            pointDiv.textContent = point;
            summaryContainer.appendChild(pointDiv);
        });
        
        // Populate topics
        const topicsContainer = document.getElementById('topicsList');
        topicsContainer.innerHTML = '';
        this.results.topics.forEach(topic => {
            const topicSpan = document.createElement('span');
            topicSpan.className = 'topic-tag';
            topicSpan.textContent = topic;
            topicsContainer.appendChild(topicSpan);
        });
        
        // Populate action items
        const actionsContainer = document.getElementById('actionItems');
        actionsContainer.innerHTML = '';
        this.results.actionItems.forEach(item => {
            const actionDiv = document.createElement('div');
            actionDiv.className = 'action-item';
            actionDiv.innerHTML = `
                <div class="action-checkbox"></div>
                <div class="action-text">${item}</div>
            `;
            actionsContainer.appendChild(actionDiv);
        });
        
        // Populate transcript
        document.getElementById('transcriptText').textContent = this.results.transcript;
        
        // Scroll to results
        document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
    }
}

// Global functions
function removeFile() {
    const app = window.audioApp;
    app.selectedFile = null;
    
    // Hide file info
    document.getElementById('fileInfo').style.display = 'none';
    
    // Reset file input
    document.getElementById('audioFile').value = '';
    
    // Disable process button
    const processBtn = document.getElementById('processBtn');
    processBtn.disabled = true;
    processBtn.style.opacity = '0.6';
}

async function processAudio() {
    const app = window.audioApp;
    
    if (!app.selectedFile) {
        app.showError('Please select an audio file first');
        return;
    }
    
    // Show processing section
    document.getElementById('processingSection').style.display = 'block';
    document.getElementById('loadingOverlay').style.display = 'flex';
    
    // Hide upload section
    document.getElementById('uploadArea').style.display = 'none';
    
    try {
        // Simulate processing steps
        await app.simulateProcessing();
        
        // Hide loading overlay
        document.getElementById('loadingOverlay').style.display = 'none';
        
        // Show results
        app.displayResults();
        
    } catch (error) {
        console.error('Processing error:', error);
        app.showError('Processing failed. Please try again.');
        document.getElementById('loadingOverlay').style.display = 'none';
    }
}

function toggleTranscript() {
    const transcriptContent = document.getElementById('transcriptContent');
    const toggleBtn = document.querySelector('.toggle-btn');
    
    if (transcriptContent.style.display === 'none') {
        transcriptContent.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide';
    } else {
        transcriptContent.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Show';
    }
}

function exportResults() {
    const app = window.audioApp;
    if (!app.results) return;
    
    // Create export data
    const exportData = {
        filename: app.selectedFile.name,
        processed_at: new Date().toISOString(),
        metadata: app.results.metadata,
        summary: app.results.summary,
        topics: app.results.topics,
        action_items: app.results.actionItems,
        transcript: app.results.transcript
    };
    
    // Create and download JSON file
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `meeting_analysis_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function shareResults() {
    const app = window.audioApp;
    if (!app.results) return;
    
    // Create shareable text
    const shareText = `
🎵 AI Meeting Analysis Results

📊 Meeting Type: ${app.results.metadata.type}
⏱️ Duration: ${app.results.metadata.duration}
😊 Sentiment: ${app.results.metadata.sentiment}
🎯 Confidence: ${app.results.metadata.confidence}

📝 Key Summary:
${app.results.summary.map(point => `• ${point}`).join('\n')}

🏷️ Topics: ${app.results.topics.join(', ')}

✅ Action Items:
${app.results.actionItems.map(item => `• ${item}`).join('\n')}

Generated by AI Meeting Transcription System
    `.trim();
    
    // Copy to clipboard
    navigator.clipboard.writeText(shareText).then(() => {
        // Show success message
        const successDiv = document.createElement('div');
        successDiv.textContent = '✅ Results copied to clipboard!';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4ade80;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            font-weight: 500;
        `;
        
        document.body.appendChild(successDiv);
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.parentNode.removeChild(successDiv);
            }
        }, 3000);
    });
}

// Initialize the app when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.audioApp = new AudioTranscriptionApp();
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOutRight {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);
});
