from flask import Blueprint, request, jsonify
import os
import uuid
import threading
import time
import datetime
import json

notes_bp = Blueprint('notes', __name__)

# Configuration
DATA_DIR = os.path.join(os.path.dirname(__file__), '../data/synthetic_data')
TRANSCRIPTS_DIR = os.path.join(DATA_DIR, 'transcripts')
os.makedirs(TRANSCRIPTS_DIR, exist_ok=True)

jobs = {}

def transcribe_audio_deepseek(audio_path):
    """Transcribe audio using mock implementation"""
    try:
        # Simulate processing time
        time.sleep(2)

        # Generate a realistic mock transcript based on file name
        filename = os.path.basename(audio_path)
        mock_transcript = f"""
Meeting Transcript - {filename}

Welcome everyone to today's meeting. Let's start by reviewing our quarterly goals and progress.

First, let's discuss our sales performance. We've seen a 15% increase in revenue compared to last quarter, which is excellent news. Our e-commerce platform has been performing particularly well.

Next, I'd like to address our upcoming product launch. The development team has been working hard, and we're on track for the scheduled release date. We need to coordinate with marketing for the promotional campaign.

For action items, we need to:
1. Finalize the marketing budget by Friday
2. Schedule a follow-up meeting with the development team
3. Review customer feedback from the beta testing phase

Let's also discuss our customer service improvements. We've implemented a new ticketing system that should help reduce response times.

Any questions or concerns before we wrap up? Great, thank you everyone for your time and contributions.
        """.strip()

        return mock_transcript
    except Exception as e:
        print(f"Transcription error: {e}")
        return f"Mock transcript for {os.path.basename(audio_path)}. This is a placeholder transcription."

def summarize_text(text):
    """Generate a mock summary"""
    try:
        # Simple mock summarization
        summary = """
**Meeting Summary (5 minutes)**

This meeting covered quarterly performance review and upcoming initiatives. Key highlights include:

• **Sales Performance**: 15% revenue increase compared to last quarter, with strong e-commerce platform performance
• **Product Launch**: Development on track for scheduled release, marketing coordination needed
• **Customer Service**: New ticketing system implemented to improve response times

The team discussed progress on current projects and identified areas for improvement. Overall, the quarter shows positive momentum with several successful initiatives.

**Next Steps**: Focus on marketing budget finalization, development team coordination, and customer feedback analysis.
        """.strip()

        return summary
    except Exception as e:
        print(f"Summarization error: {e}")
        return "This is a concise 5-minute summary. The AI summarization service is currently unavailable."

def extract_action_items(text):
    """Extract mock action items"""
    try:
        # Extract action items from the mock transcript
        action_items = [
            "Finalize the marketing budget by Friday",
            "Schedule a follow-up meeting with the development team",
            "Review customer feedback from the beta testing phase",
            "Monitor new ticketing system performance",
            "Prepare quarterly report for stakeholders"
        ]
        return action_items
    except Exception as e:
        print(f"Action items extraction error: {e}")
        return ['Follow up with participants', 'Review meeting notes', 'Schedule next meeting']

def transcribe_and_summarize(job_id, audio_path):
    """Main function to transcribe and summarize audio"""
    try:
        jobs[job_id]['status'] = 'transcribing'

        # Step 1: Transcribe audio
        raw_transcript = transcribe_audio_deepseek(audio_path)

        jobs[job_id]['status'] = 'summarizing'

        # Step 2: Generate summary
        summary = summarize_text(raw_transcript)

        # Step 3: Extract action items
        action_items = extract_action_items(raw_transcript)

        # Step 4: Store results
        jobs[job_id]['status'] = 'done'
        jobs[job_id]['result'] = {
            'date': datetime.datetime.now().strftime('%Y-%m-%d'),
            'rawTranscript': raw_transcript,
            'summary': summary,
            'actionItems': action_items
        }

    except Exception as e:
        print(f"Processing error: {e}")
        jobs[job_id]['status'] = 'error'
        jobs[job_id]['error'] = str(e)

@notes_bp.route('/transcribe', methods=['POST'])
def transcribe():
    if 'audio' not in request.files:
        return jsonify({'error': 'No audio file uploaded'}), 400
    audio = request.files['audio']
    job_id = str(uuid.uuid4())
    audio_path = os.path.join(TRANSCRIPTS_DIR, f'{job_id}.wav')
    audio.save(audio_path)
    jobs[job_id] = {'status': 'processing', 'result': None}
    threading.Thread(target=transcribe_and_summarize, args=(job_id, audio_path)).start()
    return jsonify({'job_id': job_id})

@notes_bp.route('/status')
def status():
    job_id = request.args.get('job_id')
    if not job_id or job_id not in jobs:
        return jsonify({'error': 'Invalid job_id'}), 404
    return jsonify({'status': jobs[job_id]['status']})

@notes_bp.route('/summary')
def summary():
    job_id = request.args.get('job_id')
    if not job_id or job_id not in jobs or jobs[job_id]['result'] is None:
        return jsonify({'error': 'Summary not ready'}), 404
    return jsonify(jobs[job_id]['result'])
