/* AI Meeting Transcription - Professional Styling */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ade80;
    --warning-color: #fbbf24;
    --error-color: #f87171;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header-content h1 {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 400;
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Card Base Styles */
.upload-card,
.processing-card,
.result-card {
    background: var(--bg-primary);
    border-radius: 16px;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all 0.3s ease;
}

.upload-card:hover,
.processing-card:hover,
.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* Upload Section */
.upload-section {
    animation: fadeInUp 0.6s ease-out;
}

.upload-header {
    padding: 30px 30px 0;
    text-align: center;
}

.upload-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.upload-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.upload-area {
    margin: 30px;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.02);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.upload-content p {
    color: var(--text-secondary);
    margin-bottom: 20px;
}

.browse-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.browse-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* File Info */
.file-info {
    margin: 0 30px 30px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.file-details i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.file-text {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
}

.file-size {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.remove-file {
    background: var(--error-color);
    color: white;
    border: none;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-file:hover {
    transform: scale(1.1);
}

/* Upload Actions */
.upload-actions {
    padding: 0 30px 30px;
    text-align: center;
}

.process-btn {
    background: linear-gradient(135deg, var(--success-color), #22c55e);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 200px;
}

.process-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    transform: none;
}

.process-btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Processing Section */
.processing-section {
    animation: fadeInUp 0.6s ease-out;
}

.processing-header {
    padding: 30px 30px 20px;
    text-align: center;
}

.processing-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.processing-steps {
    padding: 0 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.step {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.step.active {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.step.completed {
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.2);
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.step.completed .step-icon {
    background: var(--success-color);
    color: white;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.step-status {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step.completed .step-status i {
    color: var(--success-color);
    font-size: 1.2rem;
}

/* Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-bar {
    margin: 30px;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    padding: 0 30px 30px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Results Section */
.results-section {
    animation: fadeInUp 0.6s ease-out;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 30px 30px 0;
    background: var(--bg-primary);
    border-radius: 16px 16px 0 0;
    box-shadow: var(--shadow-lg);
}

.results-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.results-actions {
    display: flex;
    gap: 10px;
}

.export-btn,
.share-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.export-btn {
    background: var(--primary-color);
    color: white;
}

.share-btn {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.export-btn:hover,
.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 0 30px 30px;
}

.full-width {
    grid-column: 1 / -1;
}

/* Result Cards */
.card-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.card-content {
    padding: 20px;
}

/* Metadata Card */
.metadata-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.metadata-item:last-child {
    border-bottom: none;
}

.metadata-item .label {
    font-weight: 600;
    color: var(--text-secondary);
}

.metadata-item .value {
    font-weight: 600;
    color: var(--text-primary);
}

.sentiment.positive { color: var(--success-color); }
.sentiment.negative { color: var(--error-color); }
.sentiment.neutral { color: var(--warning-color); }

.urgency.high { color: var(--error-color); }
.urgency.medium { color: var(--warning-color); }
.urgency.low { color: var(--success-color); }

/* Summary Points */
.summary-point {
    padding: 15px;
    margin-bottom: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.summary-point:last-child {
    margin-bottom: 0;
}

/* Topics */
.topic-tag {
    display: inline-block;
    padding: 6px 12px;
    margin: 4px;
    background: var(--primary-color);
    color: white;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Action Items */
.action-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.action-item:last-child {
    border-bottom: none;
}

.action-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-top: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-checkbox:hover {
    border-color: var(--primary-color);
}

.action-text {
    flex: 1;
    color: var(--text-primary);
    line-height: 1.5;
}

/* Transcript */
.transcript-content {
    max-height: 400px;
    overflow-y: auto;
}

.transcript-text {
    background: var(--bg-secondary);
    padding: 20px;
    border-radius: 8px;
    line-height: 1.8;
    color: var(--text-primary);
    white-space: pre-wrap;
}

.toggle-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.toggle-btn:hover {
    background: var(--border-color);
}

/* Footer */
.footer {
    margin-top: 60px;
    text-align: center;
    color: rgba(255, 255, 255, 0.8);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: white;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .results-grid {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .results-actions {
        justify-content: center;
    }
}
