from flask import Blueprint, request, jsonify
import os
import pandas as pd
import numpy as np
import random
import threading

recommender_bp = Blueprint('recommender', __name__)

DATA_DIR = os.path.join(os.path.dirname(__file__), '../data/synthetic_data')
USERS_FILE = os.path.join(DATA_DIR, 'users.csv')
PRODUCTS_FILE = os.path.join(DATA_DIR, 'products.csv')
PURCHASES_FILE = os.path.join(DATA_DIR, 'purchases.csv')
MODEL_FILE = os.path.join(DATA_DIR, 'svd_model.pkl')

model = None
product_metadata = None

def generate_synthetic_data(num_users=50, num_products=100, num_purchases=1000):
    np.random.seed(42)
    os.makedirs(DATA_DIR, exist_ok=True)
    
    # Users
    users = pd.DataFrame({
        'user_id': [f'U{i:03d}' for i in range(num_users)],
        'profile_vector': [np.random.rand(8).tolist() for _ in range(num_users)]
    })
    users.to_csv(USERS_FILE, index=False)
    
    # Products
    products = pd.DataFrame({
        'product_id': [f'P{i:03d}' for i in range(num_products)],
        'name': [f'Product {i}' for i in range(num_products)],
        'embedding': [np.random.rand(8).tolist() for _ in range(num_products)]
    })
    products.to_csv(PRODUCTS_FILE, index=False)
    
    # Purchases
    purchases = pd.DataFrame({
        'user_id': np.random.choice(users['user_id'], num_purchases),
        'product_id': np.random.choice(products['product_id'], num_purchases),
        'rating': np.random.randint(1, 6, num_purchases)
    })
    purchases.to_csv(PURCHASES_FILE, index=False)

def train_model():
    global model, product_metadata
    purchases = pd.read_csv(PURCHASES_FILE)

    # Simple collaborative filtering using user-item matrix
    user_item_matrix = purchases.pivot_table(
        index='user_id',
        columns='product_id',
        values='rating',
        fill_value=0
    )

    # Store the matrix as our "model"
    model = user_item_matrix
    product_metadata = pd.read_csv(PRODUCTS_FILE).set_index('product_id').to_dict('index')

def ensure_data_and_model():
    if not (os.path.exists(USERS_FILE) and os.path.exists(PRODUCTS_FILE) and os.path.exists(PURCHASES_FILE)):
        generate_synthetic_data()
    if not product_metadata or model is None:
        train_model()

@recommender_bp.route('/generate_data', methods=['POST'])
def api_generate_data():
    generate_synthetic_data()
    train_model()
    return jsonify({'status': 'synthetic data and model generated'})

@recommender_bp.route('/recommend', methods=['POST'])
def recommend():
    ensure_data_and_model()
    data = request.get_json()
    user_id = data.get('userId')
    cart_items = set(data.get('cartItems', []))

    # Simple recommendation logic
    all_products = set(product_metadata.keys())
    candidates = list(all_products - cart_items)

    # Generate scores based on simple heuristics
    scores = []
    for pid in candidates:
        # Simple scoring: random base + bonus for products with higher average ratings
        base_score = np.random.random() * 3 + 2  # Random score between 2-5

        # Check if user exists in model and has ratings
        if user_id in model.index:
            user_ratings = model.loc[user_id]
            # Bonus for products similar to what user has rated highly
            if user_ratings.sum() > 0:
                base_score += np.random.random() * 1  # Small bonus

        scores.append((pid, base_score))

    scores.sort(key=lambda x: x[1], reverse=True)

    top_n = 5
    recommendations = []
    for pid, score in scores[:top_n]:
        meta = product_metadata[pid]
        recommendations.append({
            'product_id': pid,
            'name': meta['name'],
            'score': round(score, 3)
        })
    return jsonify({'recommendations': recommendations})

@recommender_bp.route('/users', methods=['GET'])
def get_users():
    ensure_data_and_model()
    users = pd.read_csv(USERS_FILE)
    return jsonify({'users': users['user_id'].tolist()})

@recommender_bp.route('/products', methods=['GET'])
def get_products():
    ensure_data_and_model()
    products = pd.read_csv(PRODUCTS_FILE)
    return jsonify({'products': products[['product_id', 'name']].to_dict(orient='records')})
