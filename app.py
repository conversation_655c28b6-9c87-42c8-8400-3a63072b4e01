from flask import Flask, request, jsonify, send_from_directory
from transcribe import transcribe_audio
from summarize import summarize_with_deepseek
import os
import traceback

app = Flask(__name__)
UPLOAD_FOLDER = "uploads"
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/style.css')
def style():
    return send_from_directory('.', 'style.css')

@app.route('/upload', methods=['POST'])
def upload_audio():
    try:
        # Check if file is present
        if 'audio' not in request.files:
            return jsonify({"error": "No file part"}), 400

        file = request.files['audio']

        # Check if file is selected
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Validate file type
        allowed_extensions = {'.mp3', '.wav', '.m4a', '.ogg', '.flac', '.aac'}
        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({"error": f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"}), 400

        # Save file
        file_path = os.path.join(UPLOAD_FOLDER, file.filename)
        file.save(file_path)

        try:
            # Step 1: Transcribe audio
            transcript = transcribe_audio(file_path)

            # Step 2: Summarize using DeepSeek
            summary = summarize_with_deepseek(transcript)

            # Clean up uploaded file
            os.remove(file_path)

            return jsonify({
                "transcript": transcript,
                "summary": summary
            })

        except Exception as e:
            # Clean up file on error
            if os.path.exists(file_path):
                os.remove(file_path)
            print(f"Processing error: {str(e)}")
            traceback.print_exc()
            return jsonify({"error": f"Processing failed: {str(e)}"}), 500

    except Exception as e:
        print(f"Upload error: {str(e)}")
        traceback.print_exc()
        return jsonify({"error": f"Upload failed: {str(e)}"}), 500

if __name__ == '__main__':
    print("Starting Flask application...")
    app.run(debug=True, host='127.0.0.1', port=5000)
