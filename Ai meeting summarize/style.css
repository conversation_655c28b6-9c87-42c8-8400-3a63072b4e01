/* AI Meeting Transcription - Modern Professional Styling */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;

    --secondary-600: #7c3aed;
    --secondary-700: #6d28d9;

    --success-500: #10b981;
    --success-600: #059669;
    --warning-500: #f59e0b;
    --error-500: #ef4444;
    --error-600: #dc2626;

    /* Neutral Colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Background */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);

    /* Text */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-tertiary: var(--gray-500);

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: linear-gradient(135deg, var(--primary-600) 0%, var(--secondary-600) 100%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navigation */
.navbar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--space-4) var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    color: white;
    font-weight: 600;
    font-size: 1.125rem;
}

.nav-brand i {
    font-size: 1.5rem;
    color: var(--primary-100);
}

.nav-status {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.875rem;
}

.nav-status i {
    color: var(--success-500);
    animation: pulse 2s infinite;
}

/* Main Application */
.main-app {
    flex: 1;
    padding: var(--space-8) var(--space-6);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Upload Section */
.upload-section {
    animation: slideInUp 0.6s ease-out;
}

.upload-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.upload-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

.upload-header {
    padding: var(--space-8) var(--space-8) var(--space-6);
    text-align: center;
    background: linear-gradient(135deg, var(--primary-50) 0%, var(--primary-100) 100%);
}

.upload-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-2);
    color: var(--text-primary);
}

.upload-header p {
    color: var(--text-secondary);
    font-size: 1.125rem;
    max-width: 600px;
    margin: 0 auto;
}

.upload-zone {
    padding: var(--space-8);
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    margin: var(--space-6);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-zone:hover {
    border-color: var(--primary-600);
    background: var(--primary-50);
}

.upload-zone.dragover {
    border-color: var(--primary-600);
    background: var(--primary-50);
    transform: scale(1.02);
}

/* Upload Content */
.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-4);
    text-align: center;
}

.upload-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-2);
}

.upload-icon i {
    font-size: 2rem;
    color: white;
}

.upload-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.upload-content p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: var(--space-4);
}

.upload-btn {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    color: white;
    border: none;
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-md);
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-700), var(--primary-600));
}

.upload-btn:active {
    transform: translateY(0);
}

.supported-formats {
    margin-top: var(--space-4);
}

.supported-formats small {
    color: var(--text-tertiary);
    font-size: 0.875rem;
}

/* File Preview */
.file-preview {
    margin: var(--space-6);
    padding: var(--space-6);
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    animation: slideInUp 0.4s ease-out;
}

.file-info {
    display: flex;
    align-items: center;
    gap: var(--space-4);
    margin-bottom: var(--space-6);
}

.file-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.file-icon i {
    font-size: 1.25rem;
    color: white;
}

.file-details {
    flex: 1;
}

.file-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    margin-bottom: var(--space-1);
}

.file-meta {
    display: flex;
    gap: var(--space-3);
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.remove-file {
    background: var(--error-500);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-file:hover {
    background: var(--error-600);
    transform: scale(1.1);
}

.upload-actions {
    text-align: center;
}

.process-btn {
    background: linear-gradient(135deg, var(--success-500), var(--success-600));
    color: white;
    border: none;
    padding: var(--space-4) var(--space-8);
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-md);
}

.process-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.process-btn:active {
    transform: translateY(0);
}

/* Processing Section */
.processing-section {
    animation: fadeInUp 0.6s ease-out;
}

.processing-header {
    padding: 30px 30px 20px;
    text-align: center;
}

.processing-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.processing-steps {
    padding: 0 30px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.step {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.step.active {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.step.completed {
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.2);
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.step.completed .step-icon {
    background: var(--success-color);
    color: white;
}

.step-content {
    flex: 1;
}

.step-content h3 {
    font-weight: 600;
    margin-bottom: 4px;
    color: var(--text-primary);
}

.step-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.step-status {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.step.completed .step-status i {
    color: var(--success-color);
    font-size: 1.2rem;
}

/* Spinner */
.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bar */
.progress-bar {
    margin: 30px;
    height: 8px;
    background: var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

.progress-text {
    text-align: center;
    padding: 0 30px 30px;
    font-weight: 600;
    color: var(--text-primary);
}

/* Results Section */
.results-section {
    animation: slideInUp 0.6s ease-out;
    margin: var(--space-8) 0;
}

.results-header {
    background: var(--bg-primary);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    box-shadow: var(--shadow-xl);
    padding: var(--space-8) var(--space-8) var(--space-6);
    text-align: center;
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: white;
}

.results-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--space-2);
}

.results-header p {
    font-size: 1.125rem;
    opacity: 0.9;
    margin-bottom: var(--space-6);
}

.results-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-3);
    flex-wrap: wrap;
}

.action-btn {
    padding: var(--space-3) var(--space-6);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    box-shadow: var(--shadow-md);
}

.export-btn {
    background: white;
    color: var(--success-600);
}

.share-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.new-btn {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Results Grid */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-6);
    padding: var(--space-8);
    background: var(--bg-primary);
    border-radius: 0 0 var(--radius-xl) var(--radius-xl);
    box-shadow: var(--shadow-xl);
}

.full-width {
    grid-column: 1 / -1;
}

/* Result Cards */
.result-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-200);
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    padding: var(--space-6) var(--space-6) var(--space-4);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.card-header h3 i {
    color: var(--primary-600);
}

.card-content {
    padding: var(--space-6);
}

/* Summary Content */
.summary-content {
    line-height: 1.7;
    color: var(--text-primary);
}

.summary-content p {
    margin-bottom: var(--space-4);
}

.summary-content ul {
    list-style: none;
    padding: 0;
}

.summary-content li {
    padding: var(--space-3);
    margin-bottom: var(--space-2);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-600);
    position: relative;
}

.summary-content li::before {
    content: '•';
    color: var(--primary-600);
    font-weight: bold;
    position: absolute;
    left: var(--space-3);
}

/* Summary Points */
.summary-point {
    padding: 15px;
    margin-bottom: 10px;
    background: var(--bg-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

.summary-point:last-child {
    margin-bottom: 0;
}

/* Topics */
.topic-tag {
    display: inline-block;
    padding: 6px 12px;
    margin: 4px;
    background: var(--primary-color);
    color: white;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Action Items */
.action-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-color);
}

.action-item:last-child {
    border-bottom: none;
}

.action-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-top: 2px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-checkbox:hover {
    border-color: var(--primary-color);
}

.action-text {
    flex: 1;
    color: var(--text-primary);
    line-height: 1.5;
}

/* Transcript */
.transcript-content {
    max-height: 400px;
    overflow-y: auto;
}

.transcript-text {
    background: var(--bg-secondary);
    padding: var(--space-6);
    border-radius: var(--radius-md);
    line-height: 1.8;
    color: var(--text-primary);
    white-space: pre-wrap;
    font-family: 'Inter', sans-serif;
    font-size: 0.9375rem;
}

.toggle-btn {
    background: var(--primary-600);
    color: white;
    border: none;
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.toggle-btn:hover {
    background: var(--primary-700);
}

/* Error Section */
.error-section {
    animation: slideInUp 0.6s ease-out;
    margin: var(--space-8) 0;
}

.error-container {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-8);
    text-align: center;
    border: 1px solid var(--error-500);
}

.error-icon {
    width: 80px;
    height: 80px;
    background: var(--error-500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
}

.error-icon i {
    font-size: 2rem;
    color: white;
}

.error-content h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-2);
}

.error-content p {
    color: var(--text-secondary);
    font-size: 1.125rem;
    margin-bottom: var(--space-6);
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-4);
    flex-wrap: wrap;
}

.retry-btn,
.new-file-btn {
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.retry-btn {
    background: var(--primary-600);
    color: white;
    border: none;
}

.new-file-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 1px solid var(--gray-300);
}

.retry-btn:hover {
    background: var(--primary-700);
}

.new-file-btn:hover {
    background: var(--bg-secondary);
}

/* Footer */
.app-footer {
    margin-top: auto;
    padding: var(--space-8) var(--space-6);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
}

.footer-info {
    color: rgba(255, 255, 255, 0.9);
}

.footer-info p {
    margin-bottom: var(--space-1);
    font-size: 0.875rem;
}

.footer-links {
    display: flex;
    gap: var(--space-6);
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
}

.footer-link:hover {
    color: white;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--space-6);
    right: var(--space-6);
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--space-3);
}

.toast {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-xl);
    padding: var(--space-4) var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    min-width: 300px;
    max-width: 400px;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid var(--primary-600);
}

.toast.success {
    border-left-color: var(--success-500);
}

.toast.error {
    border-left-color: var(--error-500);
}

.toast.warning {
    border-left-color: var(--warning-500);
}

.toast-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.toast.success .toast-icon {
    background: var(--success-500);
    color: white;
}

.toast.error .toast-icon {
    background: var(--error-500);
    color: white;
}

.toast.warning .toast-icon {
    background: var(--warning-500);
    color: white;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-1);
}

.toast-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    cursor: pointer;
    padding: var(--space-1);
    border-radius: var(--radius-sm);
    transition: all var(--transition-normal);
}

.toast-close:hover {
    background: var(--gray-100);
    color: var(--text-secondary);
}

/* Animations */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-app {
        padding: var(--space-6) var(--space-4);
    }

    .results-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: var(--space-3) var(--space-4);
    }

    .nav-brand {
        font-size: 1rem;
    }

    .upload-header h1 {
        font-size: 1.75rem;
    }

    .upload-header p {
        font-size: 1rem;
    }

    .upload-zone {
        margin: var(--space-4);
        padding: var(--space-6);
    }

    .results-header {
        padding: var(--space-6);
    }

    .results-header h2 {
        font-size: 1.5rem;
    }

    .results-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .action-btn {
        justify-content: center;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: var(--space-3);
    }

    .toast-container {
        top: var(--space-4);
        right: var(--space-4);
        left: var(--space-4);
    }

    .toast {
        min-width: auto;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .upload-header {
        padding: var(--space-6) var(--space-4) var(--space-4);
    }

    .upload-zone {
        margin: var(--space-3);
        padding: var(--space-4);
    }

    .upload-content h3 {
        font-size: 1.125rem;
    }

    .upload-btn {
        padding: var(--space-3) var(--space-4);
        font-size: 0.875rem;
    }

    .processing-header h2,
    .results-header h2 {
        font-size: 1.25rem;
    }

    .card-header {
        padding: var(--space-4);
    }

    .card-content {
        padding: var(--space-4);
    }
}
