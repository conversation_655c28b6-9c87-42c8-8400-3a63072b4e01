<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Meeting Transcription & Summarization</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Navigation -->
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-microphone-alt"></i>
                <span>AI Transcription</span>
            </div>
            <div class="nav-status" id="connectionStatus">
                <i class="fas fa-circle"></i>
                <span>Ready</span>
            </div>
        </nav>

        <!-- Main Application -->
        <main class="main-app">
            <!-- Upload Section -->
            <section class="upload-section" id="uploadSection">
                <div class="upload-container">
                    <div class="upload-header">
                        <h1>Upload Your Audio File</h1>
                        <p>Transform your meetings into intelligent summaries with AI-powered transcription</p>
                    </div>

                    <div class="upload-zone" id="uploadZone">
                        <div class="upload-content">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h3>Drag & drop your audio file here</h3>
                            <p>or click to browse your files</p>
                            <input type="file" id="file-input" accept=".mp3,.wav,.m4a,.flac,.ogg,.aac" hidden>
                            <button class="upload-btn" type="button">
                                <i class="fas fa-folder-open"></i>
                                Choose File
                            </button>
                            <div class="supported-formats">
                                <small>Supported: MP3, WAV, M4A, FLAC, OGG, AAC (Max 100MB)</small>
                            </div>
                        </div>
                    </div>

                    <!-- File Preview -->
                    <div class="file-preview" id="filePreview" style="display: none;">
                        <div class="file-info">
                            <div class="file-icon">
                                <i class="fas fa-file-audio"></i>
                            </div>
                            <div class="file-details">
                                <div class="file-name" id="file-name"></div>
                                <div class="file-meta">
                                    <span class="file-size" id="file-size"></span>
                                    <span class="file-duration" id="file-duration"></span>
                                </div>
                            </div>
                            <button class="remove-file" id="remove-file" type="button">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="upload-actions">
                            <button class="process-btn" id="process-btn" type="button">
                                <i class="fas fa-play"></i>
                                Start Processing
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Processing Section -->
            <section class="processing-section" id="processingSection" style="display: none;">
                <div class="processing-container">
                    <div class="processing-header">
                        <h2>Processing Your Audio</h2>
                        <p>AI is analyzing your audio file and generating insights</p>
                    </div>

                    <div class="progress-wrapper">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <div class="progress-info">
                            <span class="progress-text" id="progressText">Initializing...</span>
                            <span class="progress-percent" id="progressPercent">0%</span>
                        </div>
                    </div>

                    <div class="processing-steps">
                        <div class="step" id="step-upload">
                            <div class="step-indicator">
                                <i class="fas fa-upload"></i>
                            </div>
                            <div class="step-info">
                                <h4>File Upload</h4>
                                <p>Uploading and validating audio file</p>
                            </div>
                            <div class="step-status">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>

                        <div class="step" id="step-transcription">
                            <div class="step-indicator">
                                <i class="fas fa-microphone-alt"></i>
                            </div>
                            <div class="step-info">
                                <h4>Speech Recognition</h4>
                                <p>Converting audio to text using AI</p>
                            </div>
                            <div class="step-status">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>

                        <div class="step" id="step-analysis">
                            <div class="step-indicator">
                                <i class="fas fa-brain"></i>
                            </div>
                            <div class="step-info">
                                <h4>AI Analysis</h4>
                                <p>Generating summary and insights</p>
                            </div>
                            <div class="step-status">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>

                        <div class="step" id="step-extraction">
                            <div class="step-indicator">
                                <i class="fas fa-tasks"></i>
                            </div>
                            <div class="step-info">
                                <h4>Data Extraction</h4>
                                <p>Extracting action items and insights</p>
                            </div>
                            <div class="step-status">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>

                        <div class="step" id="step-completion">
                            <div class="step-indicator">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-info">
                                <h4>Completion</h4>
                                <p>Finalizing results</p>
                            </div>
                            <div class="step-status">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>

                    <div class="cancel-processing">
                        <button class="cancel-btn" id="cancelBtn" type="button">
                            <i class="fas fa-times"></i>
                            Cancel Processing
                        </button>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2>Analysis Complete</h2>
                    <p>Your audio has been successfully processed and analyzed</p>
                    <div class="results-actions">
                        <button class="action-btn export-btn" id="export-btn" type="button">
                            <i class="fas fa-download"></i>
                            Export Results
                        </button>
                        <button class="action-btn share-btn" id="share-btn" type="button">
                            <i class="fas fa-share-alt"></i>
                            Share
                        </button>
                        <button class="action-btn new-btn" id="new-btn" type="button">
                            <i class="fas fa-plus"></i>
                            New Analysis
                        </button>
                    </div>
                </div>

                <div class="results-grid">
                    <!-- Summary Card -->
                    <div class="result-card summary-card">
                        <div class="card-header">
                            <h3><i class="fas fa-list-ul"></i> Summary</h3>
                        </div>
                        <div class="card-content">
                            <div class="summary-content" id="summaryContent">
                                <!-- Summary will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Action Items Card -->
                    <div class="result-card actions-card">
                        <div class="card-header">
                            <h3><i class="fas fa-tasks"></i> Action Items</h3>
                        </div>
                        <div class="card-content">
                            <div class="actions-list" id="actionsList">
                                <!-- Action items will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Key Topics Card -->
                    <div class="result-card topics-card">
                        <div class="card-header">
                            <h3><i class="fas fa-tags"></i> Key Topics</h3>
                        </div>
                        <div class="card-content">
                            <div class="topics-list" id="topicsList">
                                <!-- Topics will be populated here -->
                            </div>
                        </div>
                    </div>
                        </div>
                    </div>

                    <!-- Full Transcript Card -->
                    <div class="result-card transcript-card full-width">
                        <div class="card-header">
                            <h3><i class="fas fa-file-alt"></i> Full Transcript</h3>
                            <button class="toggle-btn" id="toggle-transcript" type="button">
                                <i class="fas fa-chevron-down"></i>
                                <span>Show Transcript</span>
                            </button>
                        </div>
                        <div class="card-content transcript-content" id="transcriptContent" style="display: none;">
                            <div class="transcript-text" id="transcriptText">
                                <!-- Transcript will be populated here -->
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Error Section -->
            <section class="error-section" id="errorSection" style="display: none;">
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="error-content">
                        <h3>Processing Error</h3>
                        <p id="errorMessage">An error occurred while processing your audio file.</p>
                        <div class="error-actions">
                            <button class="retry-btn" id="retryBtn" type="button">
                                <i class="fas fa-redo"></i>
                                Try Again
                            </button>
                            <button class="new-file-btn" id="newFileBtn" type="button">
                                <i class="fas fa-upload"></i>
                                Upload New File
                            </button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="app-footer">
            <div class="footer-content">
                <div class="footer-info">
                    <p>&copy; 2024 AI Meeting Transcription</p>
                    <p>Powered by Deepseek API & Advanced AI</p>
                </div>
                <div class="footer-links">
                    <a href="#" class="footer-link">
                        <i class="fas fa-question-circle"></i>
                        Help
                    </a>
                    <a href="#" class="footer-link">
                        <i class="fas fa-shield-alt"></i>
                        Privacy
                    </a>
                    <a href="#" class="footer-link">
                        <i class="fas fa-cog"></i>
                        Settings
                    </a>
                </div>
            </div>
        </footer>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner-ring"></div>
            </div>
            <h3 id="loadingTitle">Processing Audio</h3>
            <p id="loadingMessage">Please wait while we analyze your file...</p>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
