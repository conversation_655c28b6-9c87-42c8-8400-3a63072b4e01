// AI Meeting Transcription - Professional Frontend JavaScript
// Real API integration with Flask backend - NO FALLBACKS

class AudioTranscriptionApp {
    constructor() {
        this.selectedFile = null;
        this.currentJobId = null;
        this.pollingInterval = null;
        this.results = null;
        this.apiBaseUrl = window.location.origin; // Use current domain

        this.initializeEventListeners();
        this.checkBackendConnection();
    }

    async checkBackendConnection() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                this.updateConnectionStatus(true);
            } else {
                this.updateConnectionStatus(false);
            }
        } catch (error) {
            console.error('Backend connection failed:', error);
            this.updateConnectionStatus(false);
        }
    }

    updateConnectionStatus(isConnected) {
        const statusIndicator = document.querySelector('.nav-status i');
        const statusText = document.querySelector('.nav-status span');

        if (isConnected) {
            statusIndicator.className = 'fas fa-circle';
            statusIndicator.style.color = '#10b981';
            if (statusText) statusText.textContent = 'Connected';
        } else {
            statusIndicator.className = 'fas fa-circle';
            statusIndicator.style.color = '#ef4444';
            if (statusText) statusText.textContent = 'Disconnected';
            this.showToast('error', 'Backend Connection Failed', 'Unable to connect to the transcription service');
        }
    }

    initializeEventListeners() {
        // File input change
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
        }

        // Drag and drop
        const uploadZone = document.querySelector('.upload-zone');
        if (uploadZone) {
            uploadZone.addEventListener('dragover', (e) => this.handleDragOver(e));
            uploadZone.addEventListener('dragleave', (e) => this.handleDragLeave(e));
            uploadZone.addEventListener('drop', (e) => this.handleFileDrop(e));
            uploadZone.addEventListener('click', () => fileInput?.click());
        }

        // Process button
        const processBtn = document.getElementById('process-btn');
        if (processBtn) {
            processBtn.addEventListener('click', () => this.processAudio());
        }

        // Remove file button
        const removeBtn = document.getElementById('remove-file');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => this.removeFile());
        }

        // Export and share buttons
        const exportBtn = document.getElementById('export-btn');
        const shareBtn = document.getElementById('share-btn');
        const newBtn = document.getElementById('new-btn');

        if (exportBtn) exportBtn.addEventListener('click', () => this.exportResults());
        if (shareBtn) shareBtn.addEventListener('click', () => this.shareResults());
        if (newBtn) newBtn.addEventListener('click', () => this.startNew());

        // Toggle transcript
        const toggleBtn = document.getElementById('toggle-transcript');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleTranscript());
        }
    }
    
    handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
            this.validateAndSetFile(file);
        }
    }

    handleDragOver(event) {
        event.preventDefault();
        event.stopPropagation();
        const uploadZone = document.querySelector('.upload-zone');
        uploadZone?.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.preventDefault();
        event.stopPropagation();
        const uploadZone = document.querySelector('.upload-zone');
        uploadZone?.classList.remove('dragover');
    }

    handleFileDrop(event) {
        event.preventDefault();
        event.stopPropagation();
        const uploadZone = document.querySelector('.upload-zone');
        uploadZone?.classList.remove('dragover');

        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.validateAndSetFile(files[0]);
        }
    }

    validateAndSetFile(file) {
        // Validate file type
        const allowedExtensions = ['.mp3', '.wav', '.m4a', '.flac', '.ogg', '.aac'];

        const fileName = file.name.toLowerCase();
        const isValidExtension = allowedExtensions.some(ext => fileName.endsWith(ext));

        if (!isValidExtension) {
            this.showToast('error', 'Invalid File Type', 'Please select a valid audio file (MP3, WAV, M4A, FLAC, OGG, AAC)');
            return;
        }

        // Validate file size (max 100MB)
        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
            this.showToast('error', 'File Too Large', 'File size must be less than 100MB');
            return;
        }

        this.selectedFile = file;
        this.displayFilePreview(file);
        this.enableProcessButton();
        this.showToast('success', 'File Selected', `${file.name} is ready for processing`);
    }
    
    displayFilePreview(file) {
        const filePreview = document.querySelector('.file-preview');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const fileDuration = document.getElementById('file-duration');

        if (fileName) fileName.textContent = file.name;
        if (fileSize) fileSize.textContent = this.formatFileSize(file.size);
        if (fileDuration) fileDuration.textContent = 'Calculating...';

        if (filePreview) {
            filePreview.style.display = 'block';
        }

        // Try to get audio duration
        this.getAudioDuration(file).then(duration => {
            if (fileDuration) {
                fileDuration.textContent = this.formatDuration(duration);
            }
        }).catch(() => {
            if (fileDuration) {
                fileDuration.textContent = 'Unknown';
            }
        });
    }

    async getAudioDuration(file) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            audio.addEventListener('loadedmetadata', () => {
                resolve(audio.duration);
            });
            audio.addEventListener('error', reject);
            audio.src = URL.createObjectURL(file);
        });
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatDuration(seconds) {
        if (isNaN(seconds) || seconds < 0) return 'Unknown';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }
    }

    enableProcessButton() {
        const processBtn = document.getElementById('process-btn');
        if (processBtn) {
            processBtn.disabled = false;
            processBtn.classList.remove('disabled');
        }
    }

    removeFile() {
        this.selectedFile = null;

        // Hide file preview
        const filePreview = document.querySelector('.file-preview');
        if (filePreview) {
            filePreview.style.display = 'none';
        }

        // Reset file input
        const fileInput = document.getElementById('file-input');
        if (fileInput) {
            fileInput.value = '';
        }

        // Disable process button
        const processBtn = document.getElementById('process-btn');
        if (processBtn) {
            processBtn.disabled = true;
            processBtn.classList.add('disabled');
        }

        this.showToast('info', 'File Removed', 'Please select another file to continue');
    }
    
    showToast(type, title, message) {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        const iconMap = {
            success: 'fas fa-check',
            error: 'fas fa-exclamation-triangle',
            warning: 'fas fa-exclamation-circle',
            info: 'fas fa-info-circle'
        };

        toast.innerHTML = `
            <div class="toast-icon">
                <i class="${iconMap[type] || iconMap.info}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${title}</div>
                <div class="toast-message">${message}</div>
            </div>
            <button class="toast-close">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add close functionality
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            this.removeToast(toast);
        });

        toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.removeToast(toast);
        }, 5000);
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container';
        document.body.appendChild(container);
        return container;
    }

    removeToast(toast) {
        if (toast && toast.parentNode) {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }

    async processAudio() {
        if (!this.selectedFile) {
            this.showToast('error', 'No File Selected', 'Please select an audio file first');
            return;
        }

        try {
            // Show processing section
            this.showProcessingSection();

            // Upload file and start processing
            const jobId = await this.uploadFile();
            this.currentJobId = jobId;

            // Start polling for status
            await this.pollProcessingStatus();

        } catch (error) {
            console.error('Processing error:', error);
            this.showError('Processing failed. Please try again.');
            this.hideProcessingSection();
        }
    }

    showProcessingSection() {
        const uploadSection = document.querySelector('.upload-section');
        const processingSection = document.querySelector('.processing-section');

        if (uploadSection) uploadSection.style.display = 'none';
        if (processingSection) processingSection.style.display = 'block';

        // Reset processing steps
        this.resetProcessingSteps();
    }

    hideProcessingSection() {
        const uploadSection = document.querySelector('.upload-section');
        const processingSection = document.querySelector('.processing-section');

        if (uploadSection) uploadSection.style.display = 'block';
        if (processingSection) processingSection.style.display = 'none';
    }

    resetProcessingSteps() {
        const steps = document.querySelectorAll('.step');
        steps.forEach(step => {
            step.classList.remove('active', 'completed');
        });

        const progressFill = document.querySelector('.progress-fill');
        const progressPercent = document.querySelector('.progress-percent');

        if (progressFill) progressFill.style.width = '0%';
        if (progressPercent) progressPercent.textContent = '0%';
    }
    
    async uploadFile() {
        const formData = new FormData();
        formData.append('audio', this.selectedFile);

        this.updateProcessingStep('upload', 'active');
        this.updateProgress(10, 'Uploading file...');

        try {
            const response = await fetch(`${this.apiBaseUrl}/upload`, {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
            }

            const result = await response.json();

            if (result.error) {
                throw new Error(result.error);
            }

            this.updateProcessingStep('upload', 'completed');
            this.updateProgress(25, 'File uploaded successfully');

            return result.job_id;

        } catch (error) {
            this.updateProcessingStep('upload', 'error');
            throw error;
        }
    }

    async pollProcessingStatus() {
        if (!this.currentJobId) {
            throw new Error('No job ID available');
        }

        this.pollingInterval = setInterval(async () => {
            try {
                const response = await fetch(`${this.apiBaseUrl}/status/${this.currentJobId}`);

                if (!response.ok) {
                    throw new Error(`Status check failed: ${response.status}`);
                }

                const status = await response.json();

                this.updateProcessingProgress(status);

                if (status.status === 'completed') {
                    clearInterval(this.pollingInterval);
                    this.results = status.result;
                    this.showResults();
                } else if (status.status === 'failed') {
                    clearInterval(this.pollingInterval);
                    throw new Error(status.error || 'Processing failed');
                }

            } catch (error) {
                clearInterval(this.pollingInterval);
                throw error;
            }
        }, 2000); // Poll every 2 seconds
    }

    updateProcessingProgress(status) {
        // Update progress bar
        const progress = status.progress || 0;
        this.updateProgress(progress, status.message || 'Processing...');

        // Update processing steps
        if (status.status === 'transcribing') {
            this.updateProcessingStep('transcription', 'active');
        } else if (status.status === 'analyzing') {
            this.updateProcessingStep('transcription', 'completed');
            this.updateProcessingStep('analysis', 'active');
        } else if (status.status === 'extracting') {
            this.updateProcessingStep('analysis', 'completed');
            this.updateProcessingStep('extraction', 'active');
        } else if (status.status === 'completed') {
            this.updateProcessingStep('extraction', 'completed');
            this.updateProcessingStep('completion', 'completed');
        }
    }

    updateProcessingStep(stepId, state) {
        const step = document.getElementById(`step-${stepId}`);
        if (step) {
            step.classList.remove('active', 'completed', 'error');
            if (state !== 'pending') {
                step.classList.add(state);
            }
        }
    }

    updateProgress(percentage, message) {
        const progressFill = document.querySelector('.progress-fill');
        const progressPercent = document.querySelector('.progress-percent');
        const progressText = document.querySelector('.progress-text');

        if (progressFill) progressFill.style.width = `${percentage}%`;
        if (progressPercent) progressPercent.textContent = `${Math.round(percentage)}%`;
        if (progressText) progressText.textContent = message;
    }
    
    showResults() {
        const processingSection = document.querySelector('.processing-section');
        const resultsSection = document.querySelector('.results-section');

        if (processingSection) processingSection.style.display = 'none';
        if (resultsSection) resultsSection.style.display = 'block';

        this.populateResults();
        this.showToast('success', 'Processing Complete', 'Your audio has been successfully transcribed and analyzed');

        // Scroll to results
        resultsSection?.scrollIntoView({ behavior: 'smooth' });
    }

    populateResults() {
        if (!this.results) return;

        // Populate summary
        const summaryContent = document.querySelector('.summary-content');
        if (summaryContent && this.results.summary) {
            if (Array.isArray(this.results.summary)) {
                summaryContent.innerHTML = `<ul>${this.results.summary.map(point => `<li>${point}</li>`).join('')}</ul>`;
            } else {
                summaryContent.innerHTML = `<p>${this.results.summary}</p>`;
            }
        }

        // Populate action items
        const actionsList = document.querySelector('.actions-list');
        if (actionsList && this.results.action_items) {
            actionsList.innerHTML = '';
            this.results.action_items.forEach(item => {
                const actionDiv = document.createElement('div');
                actionDiv.className = 'action-item';
                actionDiv.innerHTML = `
                    <div class="action-checkbox"></div>
                    <div class="action-text">${item}</div>
                `;
                actionsList.appendChild(actionDiv);
            });
        }

        // Populate topics
        const topicsList = document.querySelector('.topics-list');
        if (topicsList && this.results.topics) {
            topicsList.innerHTML = '';
            this.results.topics.forEach(topic => {
                const topicSpan = document.createElement('span');
                topicSpan.className = 'topic-tag';
                topicSpan.textContent = topic;
                topicsList.appendChild(topicSpan);
            });
        }

        // Populate transcript
        const transcriptText = document.querySelector('.transcript-text');
        if (transcriptText && this.results.transcript) {
            transcriptText.textContent = this.results.transcript;
        }

        // Update metadata if available
        if (this.results.metadata) {
            this.updateMetadata(this.results.metadata);
        }
    }

    updateMetadata(metadata) {
        const metadataElements = {
            'duration': metadata.duration,
            'type': metadata.type,
            'confidence': metadata.confidence,
            'sentiment': metadata.sentiment
        };

        Object.entries(metadataElements).forEach(([key, value]) => {
            const element = document.getElementById(`meeting-${key}`);
            if (element && value) {
                element.textContent = value;
                if (key === 'sentiment') {
                    element.className = `value sentiment ${value.toLowerCase()}`;
                }
            }
        });
    }

    showError(message) {
        const errorSection = document.querySelector('.error-section');
        const errorMessage = document.querySelector('.error-content p');

        if (errorSection) {
            errorSection.style.display = 'block';
        }

        if (errorMessage) {
            errorMessage.textContent = message;
        }

        this.showToast('error', 'Processing Failed', message);
        this.hideProcessingSection();
    }
    toggleTranscript() {
        const transcriptContent = document.querySelector('.transcript-content');
        const toggleBtn = document.getElementById('toggle-transcript');

        if (transcriptContent && toggleBtn) {
            const isHidden = transcriptContent.style.display === 'none';

            if (isHidden) {
                transcriptContent.style.display = 'block';
                toggleBtn.innerHTML = '<i class="fas fa-eye-slash"></i> Hide Transcript';
            } else {
                transcriptContent.style.display = 'none';
                toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Show Transcript';
            }
        }
    }

    exportResults() {
        if (!this.results) {
            this.showToast('warning', 'No Results', 'No results available to export');
            return;
        }

        // Create export data
        const exportData = {
            filename: this.selectedFile?.name || 'unknown',
            processed_at: new Date().toISOString(),
            summary: this.results.summary,
            topics: this.results.topics,
            action_items: this.results.action_items,
            transcript: this.results.transcript,
            metadata: this.results.metadata
        };

        // Create and download JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `meeting_analysis_${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.showToast('success', 'Export Complete', 'Results have been downloaded as JSON file');
    }

    shareResults() {
        if (!this.results) {
            this.showToast('warning', 'No Results', 'No results available to share');
            return;
        }

        // Create shareable text
        const summary = Array.isArray(this.results.summary)
            ? this.results.summary.join('\n• ')
            : this.results.summary;

        const actionItems = Array.isArray(this.results.action_items)
            ? this.results.action_items.join('\n• ')
            : 'No action items identified';

        const topics = Array.isArray(this.results.topics)
            ? this.results.topics.join(', ')
            : 'No topics identified';

        const shareText = `
🎵 AI Meeting Analysis Results

📝 Summary:
• ${summary}

🏷️ Topics: ${topics}

✅ Action Items:
• ${actionItems}

Generated by AI Meeting Transcription System
        `.trim();

        // Copy to clipboard
        navigator.clipboard.writeText(shareText).then(() => {
            this.showToast('success', 'Copied to Clipboard', 'Results have been copied and ready to share');
        }).catch(() => {
            this.showToast('error', 'Copy Failed', 'Unable to copy to clipboard');
        });
    }

    startNew() {
        // Reset application state
        this.selectedFile = null;
        this.currentJobId = null;
        this.results = null;

        // Clear polling interval
        if (this.pollingInterval) {
            clearInterval(this.pollingInterval);
            this.pollingInterval = null;
        }

        // Reset UI
        const uploadSection = document.querySelector('.upload-section');
        const processingSection = document.querySelector('.processing-section');
        const resultsSection = document.querySelector('.results-section');
        const errorSection = document.querySelector('.error-section');
        const filePreview = document.querySelector('.file-preview');

        if (uploadSection) uploadSection.style.display = 'block';
        if (processingSection) processingSection.style.display = 'none';
        if (resultsSection) resultsSection.style.display = 'none';
        if (errorSection) errorSection.style.display = 'none';
        if (filePreview) filePreview.style.display = 'none';

        // Reset file input
        const fileInput = document.getElementById('file-input');
        if (fileInput) fileInput.value = '';

        // Disable process button
        const processBtn = document.getElementById('process-btn');
        if (processBtn) {
            processBtn.disabled = true;
            processBtn.classList.add('disabled');
        }

        this.showToast('info', 'Reset Complete', 'Ready for new audio file');

        // Scroll to top
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
}

// Initialize the app when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.audioApp = new AudioTranscriptionApp();

    // Check for required elements
    const requiredElements = [
        'file-input',
        'process-btn'
    ];

    const missingElements = requiredElements.filter(id => !document.getElementById(id));

    if (missingElements.length > 0) {
        console.warn('Missing required elements:', missingElements);
    }
});
